using Core.Utilities.Mail.Models;
using Core.Utilities.Results;
using System.Threading.Tasks;

namespace Core.Utilities.Mail.Abstract
{
    public interface IEmailConfigurationService
    {
        Task<IDataResult<EmailConfiguration>> GetByCompanyIdAsync(int companyId);
        Task<IResult> LogEmailHistoryAsync(EmailHistory emailHistory);
    }

    public class EmailHistory
    {
        public int CompanyID { get; set; }
        public int? UserID { get; set; }
        public string ToEmail { get; set; }
        public string Subject { get; set; }
        public string EmailType { get; set; }
        public string Status { get; set; }
        public string ErrorMessage { get; set; }
    }
}
