using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class EmailHistory : IEntity
    {
        [Key]
        public int EmailHistoryID { get; set; }
        public int CompanyID { get; set; }
        public int? UserID { get; set; }
        public string ToEmail { get; set; }
        public string Subject { get; set; }
        public string EmailType { get; set; }
        public string Status { get; set; }
        public DateTime SentDate { get; set; }
        public string ErrorMessage { get; set; }
        public DateTime CreationDate { get; set; }
    }
}
