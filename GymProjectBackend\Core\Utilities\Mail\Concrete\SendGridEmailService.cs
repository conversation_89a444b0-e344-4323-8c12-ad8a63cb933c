using Core.Utilities.Mail.Abstract;
using Core.Utilities.Mail.Models;
using Core.Utilities.Results;
using SendGrid;
using SendGrid.Helpers.Mail;
using System;
using System.Threading.Tasks;

namespace Core.Utilities.Mail.Concrete
{
    public class SendGridEmailService : IEmailService
    {
        private readonly IEmailConfigurationService _emailConfigService;

        public SendGridEmailService(IEmailConfigurationService emailConfigService)
        {
            _emailConfigService = emailConfigService;
        }

        public async Task<IResult> SendEmailAsync(EmailMessage emailMessage)
        {
            try
            {
                // Şirket e-posta konfigürasyonunu al
                var configResult = await _emailConfigService.GetByCompanyIdAsync(emailMessage.CompanyId);
                if (!configResult.Success)
                {
                    return new ErrorResult("E-posta konfigürasyonu bulunamadı.");
                }

                var config = configResult.Data;
                var client = new SendGridClient(config.SendGridApiKey);

                var from = new EmailAddress(config.FromEmail, config.FromName);
                var to = new EmailAddress(emailMessage.ToEmail, emailMessage.ToName);

                var msg = MailHelper.CreateSingleEmail(
                    from, 
                    to, 
                    emailMessage.Subject, 
                    emailMessage.PlainTextContent, 
                    emailMessage.HtmlContent
                );

                var response = await client.SendEmailAsync(msg);

                if (response.IsSuccessStatusCode)
                {
                    // E-posta geçmişine kaydet
                    await _emailConfigService.LogEmailHistoryAsync(new EmailHistory
                    {
                        CompanyID = emailMessage.CompanyId,
                        ToEmail = emailMessage.ToEmail,
                        Subject = emailMessage.Subject,
                        EmailType = "General",
                        Status = "Sent"
                    });

                    return new SuccessResult("E-posta başarıyla gönderildi.");
                }
                else
                {
                    var errorMessage = await response.Body.ReadAsStringAsync();
                    
                    // Hata geçmişine kaydet
                    await _emailConfigService.LogEmailHistoryAsync(new EmailHistory
                    {
                        CompanyID = emailMessage.CompanyId,
                        ToEmail = emailMessage.ToEmail,
                        Subject = emailMessage.Subject,
                        EmailType = "General",
                        Status = "Failed",
                        ErrorMessage = errorMessage
                    });

                    return new ErrorResult($"E-posta gönderim hatası: {errorMessage}");
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"E-posta gönderim hatası: {ex.Message}");
            }
        }

        public async Task<IResult> SendPasswordResetEmailAsync(string email, string resetToken, string userName, int companyId)
        {
            var resetUrl = $"https://admin.gymkod.com/reset-password?token={resetToken}";
            
            var htmlContent = GetPasswordResetHtmlTemplate(userName, resetUrl);
            var plainTextContent = GetPasswordResetPlainTextTemplate(userName, resetUrl);

            var emailMessage = new EmailMessage
            {
                ToEmail = email,
                ToName = userName,
                Subject = "Şifre Sıfırlama Talebi",
                HtmlContent = htmlContent,
                PlainTextContent = plainTextContent,
                CompanyId = companyId
            };

            return await SendEmailAsync(emailMessage);
        }

        public async Task<IResult> SendPasswordChangeConfirmationAsync(string email, string userName, int companyId)
        {
            var htmlContent = GetPasswordChangeHtmlTemplate(userName);
            var plainTextContent = GetPasswordChangePlainTextTemplate(userName);

            var emailMessage = new EmailMessage
            {
                ToEmail = email,
                ToName = userName,
                Subject = "Şifre Değiştirme Onayı",
                HtmlContent = htmlContent,
                PlainTextContent = plainTextContent,
                CompanyId = companyId
            };

            return await SendEmailAsync(emailMessage);
        }

        private string GetPasswordResetHtmlTemplate(string userName, string resetUrl)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Şifre Sıfırlama</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: #007bff; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }}
        .content {{ padding: 30px; background: #f9f9f9; }}
        .button {{ display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }}
        .footer {{ padding: 20px; text-align: center; font-size: 12px; color: #666; background: #f1f1f1; border-radius: 0 0 5px 5px; }}
        .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>GymKod</h1>
        </div>
        <div class='content'>
            <h2>Şifre Sıfırlama Talebi</h2>
            <p>Merhaba <strong>{userName}</strong>,</p>
            <p>Hesabınız için şifre sıfırlama talebinde bulundunuz. Şifrenizi sıfırlamak için aşağıdaki butona tıklayın:</p>
            <div style='text-align: center;'>
                <a href='{resetUrl}' class='button'>Şifremi Sıfırla</a>
            </div>
            <div class='warning'>
                <strong>⚠️ Önemli:</strong>
                <ul>
                    <li>Bu bağlantı sadece <strong>1 saat</strong> geçerlidir.</li>
                    <li>Güvenliğiniz için bağlantıyı kimseyle paylaşmayın.</li>
                    <li>Eğer bu talebi siz yapmadıysanız, bu e-postayı görmezden gelebilirsiniz.</li>
                </ul>
            </div>
        </div>
        <div class='footer'>
            <p>Bu e-posta GymKod sistemi tarafından otomatik olarak gönderilmiştir.</p>
            <p>Sorularınız için: <EMAIL></p>
        </div>
    </div>
</body>
</html>";
        }

        private string GetPasswordResetPlainTextTemplate(string userName, string resetUrl)
        {
            return $@"
Şifre Sıfırlama Talebi

Merhaba {userName},

Hesabınız için şifre sıfırlama talebinde bulundunuz.
Şifrenizi sıfırlamak için bu bağlantıya tıklayın: {resetUrl}

ÖNEMLİ:
- Bu bağlantı sadece 1 saat geçerlidir.
- Güvenliğiniz için bağlantıyı kimseyle paylaşmayın.
- Eğer bu talebi siz yapmadıysanız, bu e-postayı görmezden gelebilirsiniz.

GymKod Sistemi
<EMAIL>
";
        }

        private string GetPasswordChangeHtmlTemplate(string userName)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Şifre Değiştirme Onayı</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: #28a745; color: white; padding: 20px; text-align: center; border-radius: 5px 5px 0 0; }}
        .content {{ padding: 30px; background: #f9f9f9; }}
        .footer {{ padding: 20px; text-align: center; font-size: 12px; color: #666; background: #f1f1f1; border-radius: 0 0 5px 5px; }}
        .success {{ background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>✅ GymKod</h1>
        </div>
        <div class='content'>
            <h2>Şifre Başarıyla Değiştirildi</h2>
            <p>Merhaba <strong>{userName}</strong>,</p>
            <div class='success'>
                <p><strong>✅ Şifreniz başarıyla değiştirildi!</strong></p>
                <p>Değişiklik tarihi: {DateTime.Now:dd.MM.yyyy HH:mm}</p>
            </div>
            <p>Eğer bu değişikliği siz yapmadıysanız, lütfen derhal bizimle iletişime geçin.</p>
        </div>
        <div class='footer'>
            <p>Bu e-posta GymKod sistemi tarafından otomatik olarak gönderilmiştir.</p>
            <p>Sorularınız için: <EMAIL></p>
        </div>
    </div>
</body>
</html>";
        }

        private string GetPasswordChangePlainTextTemplate(string userName)
        {
            return $@"
Şifre Değiştirme Onayı

Merhaba {userName},

✅ Şifreniz başarıyla değiştirildi!
Değişiklik tarihi: {DateTime.Now:dd.MM.yyyy HH:mm}

Eğer bu değişikliği siz yapmadıysanız, lütfen derhal bizimle iletişime geçin.

GymKod Sistemi
<EMAIL>
";
        }
    }
}
