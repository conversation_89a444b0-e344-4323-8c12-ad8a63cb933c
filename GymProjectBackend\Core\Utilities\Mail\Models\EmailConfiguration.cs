using Core.Entities;
using System;

namespace Core.Utilities.Mail.Models
{
    public class EmailConfiguration : IEntity
    {
        public int EmailConfigurationID { get; set; }
        public int CompanyID { get; set; }
        public string SendGridApiKey { get; set; }
        public string FromEmail { get; set; }
        public string FromName { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreationDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? DeletedDate { get; set; }
    }
}
