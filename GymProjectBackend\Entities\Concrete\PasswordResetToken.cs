using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class PasswordResetToken : IEntity
    {
        [Key]
        public int TokenID { get; set; }
        public int UserID { get; set; }
        public string Token { get; set; }
        public DateTime ExpiryDate { get; set; }
        public bool IsUsed { get; set; }
        public DateTime CreationDate { get; set; }
        public DateTime? UsedDate { get; set; }
        public string IpAddress { get; set; }
        public string UserAgent { get; set; }
    }
}
