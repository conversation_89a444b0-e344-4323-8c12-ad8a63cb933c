using Core.Utilities.Mail.Models;
using Core.Utilities.Results;
using System.Threading.Tasks;

namespace Core.Utilities.Mail.Abstract
{
    public interface IEmailService
    {
        Task<IResult> SendEmailAsync(EmailMessage emailMessage);
        Task<IResult> SendPasswordResetEmailAsync(string email, string resetToken, string userName, int companyId);
        Task<IResult> SendPasswordChangeConfirmationAsync(string email, string userName, int companyId);
    }
}
