-- <PERSON><PERSON><PERSON>ırlama E-posta Sistemi Migration
-- <PERSON><PERSON><PERSON> şifre sıfırlama için minimal tablolar

USE [GymProject]
GO

-- 1. <PERSON><PERSON><PERSON> Sıfırlama Token'ları Tablosu
CREATE TABLE [dbo].[PasswordResetTokens](
    [TokenID] [int] IDENTITY(1,1) NOT NULL,
    [UserID] [int] NOT NULL,
    [Token] [nvarchar](255) NOT NULL,
    [ExpiryDate] [datetime2](7) NOT NULL,
    [IsUsed] [bit] NOT NULL DEFAULT(0),
    [CreationDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
    [UsedDate] [datetime2](7) NULL,
    [IpAddress] [nvarchar](45) NULL,
    [UserAgent] [nvarchar](500) NULL,
    CONSTRAINT [PK_PasswordResetTokens] PRIMARY KEY CLUSTERED ([TokenID] ASC),
    CONSTRAINT [FK_PasswordResetTokens_Users] FOREIGN KEY([UserID]) 
        REFERENCES [dbo].[Users] ([UserID])
)
GO

-- 2. E-posta Konfigürasyonu Tablosu (Şirket bazlı)
CREATE TABLE [dbo].[EmailConfigurations](
    [EmailConfigurationID] [int] IDENTITY(1,1) NOT NULL,
    [CompanyID] [int] NOT NULL,
    [SendGridApiKey] [nvarchar](500) NOT NULL, -- SendGrid API Key (şifreli)
    [FromEmail] [nvarchar](255) NOT NULL,
    [FromName] [nvarchar](255) NOT NULL,
    [IsActive] [bit] NOT NULL DEFAULT(1),
    [CreationDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
    [UpdatedDate] [datetime2](7) NULL,
    [DeletedDate] [datetime2](7) NULL,
    CONSTRAINT [PK_EmailConfigurations] PRIMARY KEY CLUSTERED ([EmailConfigurationID] ASC),
    CONSTRAINT [FK_EmailConfigurations_Companies] FOREIGN KEY([CompanyID]) 
        REFERENCES [dbo].[Companies] ([CompanyID])
)
GO

-- 3. E-posta Gönderim Geçmişi (Basit)
CREATE TABLE [dbo].[EmailHistory](
    [EmailHistoryID] [int] IDENTITY(1,1) NOT NULL,
    [CompanyID] [int] NOT NULL,
    [UserID] [int] NULL,
    [ToEmail] [nvarchar](255) NOT NULL,
    [Subject] [nvarchar](255) NOT NULL,
    [EmailType] [nvarchar](50) NOT NULL, -- PasswordReset, PasswordChange
    [Status] [nvarchar](20) NOT NULL, -- Sent, Failed
    [SentDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
    [ErrorMessage] [nvarchar](max) NULL,
    [CreationDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
    CONSTRAINT [PK_EmailHistory] PRIMARY KEY CLUSTERED ([EmailHistoryID] ASC),
    CONSTRAINT [FK_EmailHistory_Companies] FOREIGN KEY([CompanyID]) 
        REFERENCES [dbo].[Companies] ([CompanyID])
)
GO

-- 4. Index'ler (Performans için)
CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_Token] 
ON [dbo].[PasswordResetTokens] ([Token]) 
WHERE [IsUsed] = 0
GO

CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_UserID_ExpiryDate] 
ON [dbo].[PasswordResetTokens] ([UserID], [ExpiryDate]) 
WHERE [IsUsed] = 0
GO

CREATE NONCLUSTERED INDEX [IX_EmailConfigurations_CompanyID] 
ON [dbo].[EmailConfigurations] ([CompanyID]) 
WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_EmailHistory_CompanyID_EmailType] 
ON [dbo].[EmailHistory] ([CompanyID], [EmailType])
GO

-- 5. Varsayılan E-posta Konfigürasyonu (Test için)
-- Bu kısmı manuel olarak dolduracaksınız
PRINT 'Şifre sıfırlama e-posta sistemi migration tamamlandı!'
PRINT 'Şimdi EmailConfigurations tablosuna SendGrid bilgilerinizi ekleyin.'
GO
