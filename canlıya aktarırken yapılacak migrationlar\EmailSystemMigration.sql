-- E-posta Sistemi Migration
-- GymProject E-posta altyapısı için gerekli tablolar

USE [GymProject]
GO

-- 1. E-posta Konfigürasyonu Tablosu (Şirket bazlı SMTP ayarları)
CREATE TABLE [dbo].[EmailConfigurations](
    [EmailConfigurationID] [int] IDENTITY(1,1) NOT NULL,
    [CompanyID] [int] NOT NULL,
    [SmtpServer] [nvarchar](255) NOT NULL DEFAULT('smtp.sendgrid.net'),
    [SmtpPort] [int] NOT NULL DEFAULT(587),
    [SmtpUsername] [nvarchar](255) NOT NULL DEFAULT('apikey'),
    [SmtpPassword] [nvarchar](500) NOT NULL, -- SendGrid API Key (şifreli)
    [FromEmail] [nvarchar](255) NOT NULL,
    [FromName] [nvarchar](255) NOT NULL,
    [EnableSsl] [bit] NOT NULL DEFAULT(1),
    [IsActive] [bit] NOT NULL DEFAULT(1),
    [CreationDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
    [UpdatedDate] [datetime2](7) NULL,
    [DeletedDate] [datetime2](7) NULL,
    CONSTRAINT [PK_EmailConfigurations] PRIMARY KEY CLUSTERED ([EmailConfigurationID] ASC),
    CONSTRAINT [FK_EmailConfigurations_Companies] FOREIGN KEY([CompanyID]) 
        REFERENCES [dbo].[Companies] ([CompanyID])
)
GO

-- 2. E-posta Şablonları Tablosu
CREATE TABLE [dbo].[EmailTemplates](
    [EmailTemplateID] [int] IDENTITY(1,1) NOT NULL,
    [CompanyID] [int] NULL, -- NULL ise sistem geneli şablon
    [TemplateName] [nvarchar](100) NOT NULL,
    [TemplateType] [nvarchar](50) NOT NULL, -- PasswordReset, Welcome, MembershipExpiry, Birthday
    [Subject] [nvarchar](255) NOT NULL,
    [HtmlBody] [nvarchar](max) NOT NULL,
    [PlainTextBody] [nvarchar](max) NULL,
    [IsActive] [bit] NOT NULL DEFAULT(1),
    [CreationDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
    [UpdatedDate] [datetime2](7) NULL,
    [DeletedDate] [datetime2](7) NULL,
    CONSTRAINT [PK_EmailTemplates] PRIMARY KEY CLUSTERED ([EmailTemplateID] ASC)
)
GO

-- 3. Şifre Sıfırlama Token'ları Tablosu
CREATE TABLE [dbo].[PasswordResetTokens](
    [TokenID] [int] IDENTITY(1,1) NOT NULL,
    [UserID] [int] NOT NULL,
    [Token] [nvarchar](255) NOT NULL,
    [ExpiryDate] [datetime2](7) NOT NULL,
    [IsUsed] [bit] NOT NULL DEFAULT(0),
    [CreationDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
    [UsedDate] [datetime2](7) NULL,
    [IpAddress] [nvarchar](45) NULL,
    [UserAgent] [nvarchar](500) NULL,
    CONSTRAINT [PK_PasswordResetTokens] PRIMARY KEY CLUSTERED ([TokenID] ASC),
    CONSTRAINT [FK_PasswordResetTokens_Users] FOREIGN KEY([UserID]) 
        REFERENCES [dbo].[Users] ([UserID])
)
GO

-- 4. E-posta Kuyruğu Tablosu (Hangfire ile kullanılacak)
CREATE TABLE [dbo].[EmailQueue](
    [EmailQueueID] [int] IDENTITY(1,1) NOT NULL,
    [CompanyID] [int] NOT NULL,
    [ToEmail] [nvarchar](255) NOT NULL,
    [ToName] [nvarchar](255) NULL,
    [Subject] [nvarchar](255) NOT NULL,
    [HtmlBody] [nvarchar](max) NOT NULL,
    [PlainTextBody] [nvarchar](max) NULL,
    [Priority] [int] NOT NULL DEFAULT(1), -- 1=Normal, 2=High, 3=Critical
    [Status] [nvarchar](20) NOT NULL DEFAULT('Pending'), -- Pending, Sent, Failed, Cancelled
    [ScheduledDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
    [SentDate] [datetime2](7) NULL,
    [ErrorMessage] [nvarchar](max) NULL,
    [RetryCount] [int] NOT NULL DEFAULT(0),
    [MaxRetries] [int] NOT NULL DEFAULT(3),
    [CreationDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
    [UpdatedDate] [datetime2](7) NULL,
    CONSTRAINT [PK_EmailQueue] PRIMARY KEY CLUSTERED ([EmailQueueID] ASC),
    CONSTRAINT [FK_EmailQueue_Companies] FOREIGN KEY([CompanyID]) 
        REFERENCES [dbo].[Companies] ([CompanyID])
)
GO

-- 5. E-posta Gönderim Geçmişi Tablosu
CREATE TABLE [dbo].[EmailHistory](
    [EmailHistoryID] [int] IDENTITY(1,1) NOT NULL,
    [CompanyID] [int] NOT NULL,
    [UserID] [int] NULL, -- Gönderen kullanıcı (NULL ise sistem)
    [ToEmail] [nvarchar](255) NOT NULL,
    [Subject] [nvarchar](255) NOT NULL,
    [EmailType] [nvarchar](50) NOT NULL, -- PasswordReset, Welcome, etc.
    [Status] [nvarchar](20) NOT NULL, -- Sent, Failed, Bounced
    [SentDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
    [ErrorMessage] [nvarchar](max) NULL,
    [SendGridMessageId] [nvarchar](255) NULL, -- SendGrid tracking için
    [CreationDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
    CONSTRAINT [PK_EmailHistory] PRIMARY KEY CLUSTERED ([EmailHistoryID] ASC),
    CONSTRAINT [FK_EmailHistory_Companies] FOREIGN KEY([CompanyID])
        REFERENCES [dbo].[Companies] ([CompanyID])
)
GO

-- 6. Index'ler (Performans için)
CREATE NONCLUSTERED INDEX [IX_EmailConfigurations_CompanyID]
ON [dbo].[EmailConfigurations] ([CompanyID])
WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_EmailTemplates_CompanyID_TemplateType]
ON [dbo].[EmailTemplates] ([CompanyID], [TemplateType])
WHERE [IsActive] = 1
GO

CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_Token]
ON [dbo].[PasswordResetTokens] ([Token])
WHERE [IsUsed] = 0
GO

CREATE NONCLUSTERED INDEX [IX_PasswordResetTokens_UserID_ExpiryDate]
ON [dbo].[PasswordResetTokens] ([UserID], [ExpiryDate])
WHERE [IsUsed] = 0
GO

CREATE NONCLUSTERED INDEX [IX_EmailQueue_Status_ScheduledDate]
ON [dbo].[EmailQueue] ([Status], [ScheduledDate])
GO

CREATE NONCLUSTERED INDEX [IX_EmailQueue_CompanyID_Status]
ON [dbo].[EmailQueue] ([CompanyID], [Status])
GO

CREATE NONCLUSTERED INDEX [IX_EmailHistory_CompanyID_SentDate]
ON [dbo].[EmailHistory] ([CompanyID], [SentDate] DESC)
GO

-- 7. Hangfire Tabloları (Background Job için)
-- Hangfire otomatik oluşturacak, sadece schema hazırlığı
PRINT 'Hangfire tabloları otomatik oluşturulacak...'
GO

PRINT 'E-posta sistemi migration tamamlandı!'
GO
