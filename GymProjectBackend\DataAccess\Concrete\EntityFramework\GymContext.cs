﻿﻿using Core.Entities.Concrete;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;

namespace DataAccess.Concrete.EntityFramework
{
    public class GymContext:DbContext
    {
        // Constructor injection (Scalability için - sadece DI pattern)
        public GymContext(DbContextOptions<GymContext> options) : base(options) { }
        public DbSet<City> Cities { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<CompanyAdress> CompanyAdresses { get; set; }
        public DbSet<EntryExitHistory> EntryExitHistories { get; set; }
        public DbSet<Member> Members { get; set; }
        public DbSet<Membership> Memberships { get; set; }
        public DbSet<MembershipType> MembershipTypes { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<Town> Towns { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<UserCompany> UserCompanies { get; set; }
        public DbSet<OperationClaim> OperationClaims { get; set; }
        public DbSet<UserOperationClaim> UserOperationClaims { get; set; }
        public DbSet<CompanyUser > CompanyUsers { get; set; }
        public DbSet<Transaction> Transactions { get; set; }
        public DbSet<Product> Products { get; set; }
        public DbSet<DebtPayment> DebtPayments { get; set; }
        public DbSet<RemainingDebt> RemainingDebts { get; set; }
        public DbSet<UserDevice> UserDevices { get; set; }
        public DbSet<MembershipFreezeHistory> MembershipFreezeHistory { get; set; }

        public DbSet<LicensePackage> LicensePackages { get; set; }
        public DbSet<UserLicense> UserLicenses { get; set; }
        public DbSet<LicenseTransaction> LicenseTransactions { get; set; }
        public DbSet<Expense> Expenses { get; set; } // Giderler tablosu eklendi

        // Egzersiz Sistemi Tabloları
        public DbSet<ExerciseCategory> ExerciseCategories { get; set; }
        public DbSet<SystemExercise> SystemExercises { get; set; }
        public DbSet<CompanyExercise> CompanyExercises { get; set; }

        // Antrenman Programı Sistemi Tabloları
        public DbSet<WorkoutProgramTemplate> WorkoutProgramTemplates { get; set; }
        public DbSet<WorkoutProgramDay> WorkoutProgramDays { get; set; }
        public DbSet<WorkoutProgramExercise> WorkoutProgramExercises { get; set; }
        public DbSet<MemberWorkoutProgram> MemberWorkoutPrograms { get; set; }

        // E-posta Sistemi Tabloları
        public DbSet<PasswordResetToken> PasswordResetTokens { get; set; }
        public DbSet<EmailConfiguration> EmailConfigurations { get; set; }
        public DbSet<EmailHistory> EmailHistories { get; set; }
    }
}
